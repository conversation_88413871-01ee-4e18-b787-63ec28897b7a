/**
 * Utility functions for Simplified Policy Claims API Workflow Configuration
 */

import type {
  SimplifiedPolicyClaimsWorkflowConfig,
  WorkflowValidationResult,
  WorkflowExecutionContext
} from './policy-claims-workflow.types';

// Import the workflow configuration
import workflowConfig from './policy-claims-workflow.json';

/**
 * Load the simplified workflow configuration with type safety
 */
export function loadWorkflowConfig(): SimplifiedPolicyClaimsWorkflowConfig {
  return workflowConfig as SimplifiedPolicyClaimsWorkflowConfig;
}

/**
 * Validate the simplified workflow configuration structure
 */
export function validateWorkflowConfig(config: SimplifiedPolicyClaimsWorkflowConfig): WorkflowValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate basic metadata
  if (!config.name) {
    errors.push('Workflow name is required');
  }
  if (!config.version) {
    errors.push('Workflow version is required');
  }

  // Validate configuration mode
  if (!['database', 'fixed', 'hybrid'].includes(config.config.mode)) {
    errors.push('Configuration mode must be "database", "fixed", or "hybrid"');
  }

  // Validate workflow steps
  if (!config.steps?.length) {
    errors.push('At least one workflow step is required');
  } else {
    // Validate step IDs are sequential
    const sortedSteps = [...config.steps].sort((a, b) => a.id - b.id);
    for (let i = 0; i < sortedSteps.length; i++) {
      if (sortedSteps[i].id !== i + 1) {
        errors.push(`Step IDs should be sequential. Expected ${i + 1}, got ${sortedSteps[i].id}`);
      }
    }

    // Validate API configurations
    config.steps.forEach(step => {
      if (!step.endpoint) {
        errors.push(`Step "${step.name}" missing API endpoint`);
      }
      if (!step.method) {
        errors.push(`Step "${step.name}" missing HTTP method`);
      }
      if (!step.extract || Object.keys(step.extract).length === 0) {
        warnings.push(`Step "${step.name}" has no data extraction defined`);
      }
    });
  }

  // Validate storage configuration
  if (!config.storage?.table) {
    warnings.push('No storage table defined');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Extract template variables from a string
 */
export function extractTemplateVariables(text: string): string[] {
  const templateRegex = /\{\{([^}]+)\}\}/g;
  const variables: string[] = [];
  let match;

  while ((match = templateRegex.exec(text)) !== null) {
    variables.push(match[1]);
  }

  return variables;
}

/**
 * Resolve template variables in a string using execution context
 */
export function resolveTemplateVariables(
  text: string,
  context: WorkflowExecutionContext
): string {
  return text.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
    // Handle simplified template variables
    if (context.step_data[variable]) {
      return context.step_data[variable];
    }

    if (context.database_data[variable]) {
      return context.database_data[variable];
    }

    if (variable === 'timestamp') {
      return new Date().toISOString();
    }

    if (variable.startsWith('input.')) {
      const inputKey = variable.replace('input.', '');
      return (context as any)[inputKey] || match;
    }

    return match; // Return original if not found
  });
}

/**
 * Get step by ID
 */
export function getStepById(config: SimplifiedPolicyClaimsWorkflowConfig, stepId: number) {
  return config.steps.find(step => step.id === stepId);
}

/**
 * Get step by name
 */
export function getStepByName(config: SimplifiedPolicyClaimsWorkflowConfig, stepName: string) {
  return config.steps.find(step => step.name === stepName);
}

/**
 * Get steps in execution order
 */
export function getStepsInOrder(config: SimplifiedPolicyClaimsWorkflowConfig) {
  return [...config.steps].sort((a, b) => a.id - b.id);
}

/**
 * Create execution context for workflow
 */
export function createExecutionContext(customerId: string): WorkflowExecutionContext {
  return {
    customer_id: customerId,
    execution_id: `exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    started_at: new Date(),
    current_step: 0,
    step_data: {},
    database_data: {}
  };
}

/**
 * Get workflow summary for logging/monitoring
 */
export function getWorkflowSummary(config: SimplifiedPolicyClaimsWorkflowConfig) {
  return {
    name: config.name,
    version: config.version,
    total_steps: config.steps.length,
    estimated_duration_minutes: config.options.timeout_minutes,
    mode: config.config.mode,
    storage_table: config.storage.table
  };
}

/**
 * Resolve data source based on configuration mode
 */
export function resolveDataSource(config: SimplifiedPolicyClaimsWorkflowConfig) {
  const mode = config.config.mode;

  if (mode === 'fixed') {
    return {
      social_id: config.config.fixed_values.social_id,
      channel_id: config.config.fixed_values.channel_id,
      citizen_id: config.config.fixed_values.citizen_id
    };
  }

  if (mode === 'hybrid') {
    return {
      // These would be read from database in actual implementation
      social_id: '{{database_lookup}}',
      channel_id: '{{database_lookup}}',
      citizen_id: config.config.fixed_values.citizen_id
    };
  }

  // Database mode
  return {
    social_id: '{{database_lookup}}',
    channel_id: '{{database_lookup}}',
    citizen_id: '{{from_step_2}}'
  };
}
